# 新手引导功能设置说明

## 安装依赖

在客户端目录下运行以下命令安装react-joyride依赖：

```bash
cd client
npm install react-joyride
# 或者使用 pnpm
pnpm add react-joyride
```

## 功能说明

新手引导功能已经集成到客户端中，包含以下特性：

### 1. 自动触发
- 用户首次登录后会自动开始新手引导
- 引导会在页面完全加载后1秒开始

### 2. 引导内容
引导包含以下步骤：
1. 欢迎介绍
2. AIZY平台标志介绍
3. AI对话功能介绍
4. 智能体市场介绍
5. 课程学习功能介绍
6. 知识库功能介绍
7. 数字人功能介绍
8. 聊天历史介绍
9. 用户中心介绍
10. 设置功能介绍
11. 完成引导

### 3. 用户控制
- 用户可以跳过引导
- 用户可以在设置中重新开始引导
- 引导状态会保存在localStorage中

### 4. 样式定制
- 使用了与网站主题一致的颜色方案
- 支持深色/浅色主题
- 响应式设计

## 文件结构

```
client/src/core/components/Onboarding/
├── index.ts                 # 导出文件
├── OnboardingTour.tsx      # 主组件
├── useOnboarding.ts        # 状态管理Hook
└── tourSteps.ts            # 引导步骤配置
```

## 使用方法

新手引导已经集成到MainLayout中，会自动工作。如果需要手动控制，可以使用：

```typescript
import { useOnboarding } from '~/core/components/Onboarding';

const { startTour, resetTour, skipTour } = useOnboarding();

// 开始引导
startTour();

// 重置并重新开始引导
resetTour();

// 跳过引导
skipTour();
```

## 自定义引导步骤

如需修改引导步骤，编辑 `tourSteps.ts` 文件：

```typescript
export const tourSteps: Step[] = [
  {
    target: '[data-tour="your-element"]',
    content: '您的引导内容',
    placement: 'bottom',
  },
  // 添加更多步骤...
];
```

## 注意事项

1. 确保目标元素有正确的 `data-tour` 属性
2. 引导只在用户已登录时显示
3. 引导状态会持久化保存
4. 可以通过设置菜单重新开始引导
