import { Step } from 'react-joyride';

/**
 * 引导步骤标题组件
 */
const StepTitle = ({ children }: { children: React.ReactNode }) => (
  <div style={{
    fontSize: '18px',
    fontWeight: 600,
    color: '#4951eb',
    marginBottom: '8px',
    padding: '4px 8px',
    backgroundColor: 'rgba(73, 81, 235, 0.1)',
    borderRadius: '6px',
    display: 'inline-block'
  }}>
    {children}
  </div>
);

/**
 * 新手引导步骤配置
 */
export const tourSteps: Step[] = [
  {
    target: 'body',
    content: (
      <div>
        <StepTitle>欢迎来到 AIZY！</StepTitle>
        <p>让我们快速了解一下新版界面的使用方法</p>
      </div>
    ),
    placement: 'center',
    disableBeacon: true,
  },
  {
    target: '[data-tour="logo"]',
    content: (
      <div>
        <StepTitle>平台标志</StepTitle>
        <p>点击可以回到聊天首页</p>
      </div>
    ),
    placement: 'bottom',
  },
  {
    target: '[data-tour="nav-chat"]',
    content: (
      <div>
        <StepTitle>AI 对话</StepTitle>
        <p>与AI进行智能对话，支持文本、图片等多种形式</p>
      </div>
    ),
    placement: 'right',
  },
  {
    target: '[data-tour="nav-agent"]',
    content: (
      <div>
        <StepTitle>智能体 & 工作流 & 数字人</StepTitle>
        <p>浏览专业AI智能体、工作流模板和数字人功能</p>
      </div>
    ),
    placement: 'right',
  },
  {
    target: '[data-tour="nav-course"]',
    content: (
      <div>
        <StepTitle>课程学习</StepTitle>
        <p>系统学习AI知识，提升技能水平</p>
      </div>
    ),
    placement: 'right',
  },
  {
    target: '[data-tour="nav-knowledge"]',
    content: (
      <div>
        <StepTitle>知识库</StepTitle>
        <p>上传文档，让AI帮助管理和检索知识</p>
      </div>
    ),
    placement: 'right',
  },
  {
    target: '[data-tour="chat-history"]',
    content: (
      <div>
        <StepTitle>聊天历史</StepTitle>
        <p>查看和管理历史对话记录</p>
      </div>
    ),
    placement: 'right',
  },
  {
    target: '[data-tour="user-center"]',
    content: (
      <div>
        <StepTitle>用户中心</StepTitle>
        <p>管理账户、查看使用情况和个人资产</p>
      </div>
    ),
    placement: 'right',
  },
  {
    target: '[data-tour="settings"]',
    content: (
      <div>
        <StepTitle>设置</StepTitle>
        <p>调整主题、语言等个人偏好</p>
      </div>
    ),
    placement: 'top',
  },
];

/**
 * 聊天页面引导步骤
 */
export const chatPageSteps: Step[] = [
  {
    target: '[data-tour="chat-input"]',
    content: (
      <div>
        <StepTitle>聊天输入框</StepTitle>
        <p>在这里输入您的问题或指令</p>
      </div>
    ),
    placement: 'top',
  },
  {
    target: '[data-tour="chat-send"]',
    content: (
      <div>
        <StepTitle>发送消息</StepTitle>
        <p>点击发送按钮或使用 Shift+Enter 快捷键发送消息</p>
      </div>
    ),
    placement: 'top',
  },
  {
    target: '[data-tour="chat-input"]',
    content: (
      <div>
        <StepTitle>换行技巧</StepTitle>
        <p>直接按 Enter 键可以换行，Shift+Enter 发送消息</p>
      </div>
    ),
    placement: 'top',
  },
];

/**
 * 智能体市场页面引导步骤
 */
export const agentMarketSteps: Step[] = [
  {
    target: '[data-tour="agent-card"]',
    content: (
      <div>
        <StepTitle>智能体卡片</StepTitle>
        <p>点击卡片查看智能体详情和功能介绍</p>
      </div>
    ),
    placement: 'bottom',
  },
  {
    target: '[data-tour="agent-purchase"]',
    content: (
      <div>
        <StepTitle>购买智能体</StepTitle>
        <p>选择付费计划，支持支付宝和微信支付</p>
      </div>
    ),
    placement: 'top',
  },
  {
    target: '[data-tour="agent-chat"]',
    content: (
      <div>
        <StepTitle>开始对话</StepTitle>
        <p>购买后点击"开始对话"即可使用智能体</p>
      </div>
    ),
    placement: 'top',
  },
];

/**
 * 完成引导步骤
 */
export const completionStep: Step = {
  target: 'body',
  content: (
    <div>
      <StepTitle>引导完成！</StepTitle>
      <p>您已掌握了新版界面的基本使用方法</p>
      <p>如需重新查看引导，请在设置中点击"重新开始引导"</p>
    </div>
  ),
  placement: 'center',
};

/**
 * 引导样式配置
 */
export const tourStyles = {
  options: {
    primaryColor: '#4951eb',
    backgroundColor: '#ffffff',
    textColor: '#333333',
    overlayColor: 'rgba(0, 0, 0, 0.5)',
    spotlightShadow: '0 0 15px rgba(0, 0, 0, 0.5)',
    beaconSize: 36,
    zIndex: 10000,
  },
  tooltip: {
    borderRadius: 8,
    fontSize: 14,
    padding: 20,
  },
  tooltipContainer: {
    textAlign: 'left' as const,
  },
  tooltipTitle: {
    fontSize: 18,
    fontWeight: 600,
    marginBottom: 8,
  },
  tooltipContent: {
    lineHeight: 1.5,
  },
  buttonNext: {
    backgroundColor: '#4951eb',
    borderRadius: 6,
    color: '#ffffff',
    fontSize: 14,
    fontWeight: 500,
    padding: '8px 16px',
    border: 'none',
    cursor: 'pointer',
  },
  buttonBack: {
    backgroundColor: 'transparent',
    border: '1px solid #d1d5db',
    borderRadius: 6,
    color: '#6b7280',
    fontSize: 14,
    fontWeight: 500,
    padding: '8px 16px',
    cursor: 'pointer',
    marginRight: 8,
  },
  buttonSkip: {
    backgroundColor: 'transparent',
    border: 'none',
    color: '#6b7280',
    fontSize: 14,
    cursor: 'pointer',
    padding: '8px 16px',
  },
  buttonClose: {
    backgroundColor: 'transparent',
    border: 'none',
    color: '#6b7280',
    fontSize: 18,
    cursor: 'pointer',
    position: 'absolute' as const,
    right: 8,
    top: 8,
  },
};
