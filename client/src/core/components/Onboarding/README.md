# 新手引导组件

这是一个基于 react-joyride 的新手引导系统，为登录用户提供网站功能介绍。

## 功能特性

- ✅ 自动检测首次登录用户
- ✅ 多页面引导流程（主页面 → 聊天页面）
- ✅ 清晰的标题样式区分
- ✅ 简洁明了的引导内容
- ✅ 支持跳过和重新开始
- ✅ 状态持久化保存
- ✅ 响应式设计
- ✅ 主题适配（深色/浅色）
- ✅ 中文本地化
- ✅ 重新开始时自动跳转到首页

## 组件结构

```
Onboarding/
├── index.ts              # 导出文件
├── OnboardingTour.tsx    # 主引导组件
├── OnboardingDebug.tsx   # 调试组件（开发用）
├── useOnboarding.ts      # 状态管理Hook
├── tourSteps.tsx         # 引导步骤配置
└── README.md            # 说明文档
```

## 引导流程

### 主页面引导
1. **欢迎介绍** - 整体平台介绍
2. **平台标志** - 品牌和导航介绍
3. **AI对话** - 聊天功能介绍
4. **智能体 & 工作流 & 数字人** - 合并介绍
5. **课程学习** - 课程功能介绍
6. **知识库** - 知识管理功能介绍
7. **聊天历史** - 历史记录功能介绍
8. **用户中心** - 个人中心功能介绍
9. **设置** - 设置功能介绍

### 聊天页面引导
1. **聊天输入框** - 输入区域介绍
2. **发送消息** - 发送按钮和快捷键介绍
3. **换行技巧** - 换行操作说明

### 智能体市场引导
1. **智能体卡片** - 卡片点击介绍
2. **购买智能体** - 支付流程介绍
3. **开始对话** - 使用智能体介绍

## 使用方法

### 基本使用

引导组件已集成到 `MainLayout` 中，会自动工作：

```tsx
import { OnboardingTour } from '~/core/components/Onboarding';

function App() {
  return (
    <div>
      {/* 你的应用内容 */}
      <OnboardingTour />
    </div>
  );
}
```

### 手动控制

```tsx
import { useOnboarding } from '~/core/components/Onboarding';

function MyComponent() {
  const { 
    startTour, 
    resetTour, 
    skipTour, 
    startSpecificTour,
    run, 
    isCompleted,
    currentTourType
  } = useOnboarding();

  return (
    <div>
      <button onClick={startTour}>开始引导</button>
      <button onClick={resetTour}>重新开始</button>
      <button onClick={skipTour}>跳过引导</button>
      <button onClick={() => startSpecificTour('chat')}>开始聊天引导</button>
      
      <p>引导状态: {run ? '运行中' : '已停止'}</p>
      <p>是否完成: {isCompleted ? '是' : '否'}</p>
      <p>当前类型: {currentTourType}</p>
    </div>
  );
}
```

## 配置说明

### 标题样式组件

引导步骤使用了自定义的标题组件：

```tsx
const StepTitle = ({ children }: { children: React.ReactNode }) => (
  <div style={{
    fontSize: '18px',
    fontWeight: 600,
    color: '#4951eb',
    marginBottom: '8px',
    padding: '4px 8px',
    backgroundColor: 'rgba(73, 81, 235, 0.1)',
    borderRadius: '6px',
    display: 'inline-block'
  }}>
    {children}
  </div>
);
```

### 添加新的引导步骤

在 `tourSteps.tsx` 中添加新步骤：

```typescript
export const tourSteps: Step[] = [
  // 现有步骤...
  {
    target: '[data-tour="your-element"]',
    content: (
      <div>
        <StepTitle>步骤标题</StepTitle>
        <p>步骤描述内容</p>
      </div>
    ),
    placement: 'bottom',
  },
];
```

### 为元素添加引导标识

在需要引导的元素上添加 `data-tour` 属性：

```tsx
<div data-tour="my-feature">
  我的功能组件
</div>
```

## API 参考

### useOnboarding Hook

```typescript
const {
  // 状态
  run,              // boolean - 引导是否正在运行
  stepIndex,        // number - 当前步骤索引
  isCompleted,      // boolean - 是否已完成引导
  isSkipped,        // boolean - 是否已跳过引导
  shouldShowTour,   // boolean - 是否应该显示引导
  currentTourType,  // 'main' | 'chat' | 'agent' - 当前引导类型
  
  // 方法
  handleJoyrideCallback, // 引导回调处理
  startTour,        // () => void - 开始引导
  stopTour,         // () => void - 停止引导
  resetTour,        // () => void - 重置并重新开始引导
  skipTour,         // () => void - 跳过引导
  startSpecificTour, // (type) => void - 开始特定类型的引导
} = useOnboarding();
```

### 本地存储

引导状态保存在以下localStorage键中：
- `aizy-onboarding-completed` - 是否已完成引导
- `aizy-onboarding-skipped` - 是否已跳过引导
- `aizy-onboarding-current-step` - 当前步骤（临时存储）

## 开发调试

使用 `OnboardingDebug` 组件进行调试：

```tsx
import { OnboardingDebug } from '~/core/components/Onboarding';

function DebugPage() {
  return (
    <div>
      <OnboardingDebug />
      {/* 其他内容 */}
    </div>
  );
}
```

## 重要改进

### 1. 清晰的标题样式
- 使用 `StepTitle` 组件包装步骤名称
- 蓝色背景和边框，提高视觉区分度

### 2. 简洁的引导内容
- 大幅简化描述文字
- 突出核心功能和操作方法

### 3. 多页面引导流程
- 主页面引导完成后自动跳转到聊天页面
- 支持不同页面的专门引导

### 4. 重新开始引导优化
- 点击"重新开始引导"自动跳转到 `/chat` 页面
- 确保引导从正确的页面开始

### 5. 合并相似功能
- 将智能体、工作流、数字人合并为一个引导步骤
- 减少引导步骤数量，提高效率

## 注意事项

1. 引导只在用户已登录时显示
2. 确保目标元素在引导运行时已渲染
3. 避免在引导过程中进行页面跳转
4. 调试组件仅用于开发，生产环境应移除
5. 引导步骤的target选择器必须准确匹配DOM元素
