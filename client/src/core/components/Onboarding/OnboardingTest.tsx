import React, { useEffect, useState } from 'react';
import { Button, Stack, Text, Paper, Group, Badge, Alert } from '@mantine/core';
import { useOnboarding } from './useOnboarding';
import { useLocation } from 'react-router-dom';

/**
 * 新手引导测试组件
 * 用于验证修复后的功能是否正常工作
 */
const OnboardingTest: React.FC = () => {
  const location = useLocation();
  const [testResults, setTestResults] = useState<string[]>([]);
  const {
    run,
    stepIndex,
    isCompleted,
    isSkipped,
    shouldShowTour,
    currentTourType,
    shouldStartFlag,
    startTour,
    stopTour,
    resetTour,
    skipTour,
    startSpecificTour,
    markShouldStartOnboarding,
  } = useOnboarding();

  // 监听状态变化
  useEffect(() => {
    const timestamp = new Date().toLocaleTimeString();
    if (run) {
      setTestResults(prev => [...prev, `${timestamp}: 引导开始运行`]);
    }
  }, [run]);

  useEffect(() => {
    const timestamp = new Date().toLocaleTimeString();
    if (shouldStartFlag) {
      setTestResults(prev => [...prev, `${timestamp}: 检测到启动标记`]);
    }
  }, [shouldStartFlag]);

  // 测试函数
  const testLoginTrigger = () => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [...prev, `${timestamp}: 模拟登录触发`]);
    markShouldStartOnboarding();
  };

  const testResetTour = () => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [...prev, `${timestamp}: 重置引导`]);
    resetTour();
  };

  const clearTestResults = () => {
    setTestResults([]);
  };

  const clearAllStorage = () => {
    localStorage.removeItem('aizy-onboarding-completed');
    localStorage.removeItem('aizy-onboarding-skipped');
    localStorage.removeItem('aizy-onboarding-should-start');
    localStorage.removeItem('aizy-onboarding-current-step');
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [...prev, `${timestamp}: 清除所有localStorage`]);
  };

  return (
    <Paper p="md" withBorder style={{ position: 'fixed', top: 20, left: 20, zIndex: 10001, width: 400 }}>
      <Stack gap="xs">
        <Text size="sm" fw={600}>新手引导测试面板</Text>
        
        <Group gap="xs">
          <Text size="xs">当前页面:</Text>
          <Badge size="xs" variant="light">{location.pathname}</Badge>
        </Group>

        <Group gap="xs">
          <Text size="xs">引导类型:</Text>
          <Badge size="xs" color={currentTourType === 'main' ? 'blue' : currentTourType === 'chat' ? 'green' : 'orange'}>
            {currentTourType}
          </Badge>
        </Group>

        <Group gap="xs">
          <Text size="xs">运行状态:</Text>
          <Badge size="xs" color={run ? 'green' : 'gray'}>
            {run ? '运行中' : '已停止'}
          </Badge>
        </Group>

        <Group gap="xs">
          <Text size="xs">启动标记:</Text>
          <Badge size="xs" color={shouldStartFlag ? 'yellow' : 'gray'}>
            {shouldStartFlag ? '是' : '否'}
          </Badge>
        </Group>

        <Group gap="xs">
          <Text size="xs">当前步骤:</Text>
          <Badge size="xs">{stepIndex}</Badge>
        </Group>

        <Stack gap="xs">
          <Text size="xs" fw={500}>测试功能:</Text>
          
          <Group gap="xs">
            <Button size="xs" onClick={testLoginTrigger} color="blue">
              测试登录触发
            </Button>
            <Button size="xs" onClick={testResetTour} color="green">
              测试重置引导
            </Button>
          </Group>
          
          <Group gap="xs">
            <Button size="xs" onClick={clearAllStorage} color="red" variant="light">
              清除存储
            </Button>
            <Button size="xs" onClick={clearTestResults} color="gray" variant="light">
              清除日志
            </Button>
          </Group>
        </Stack>

        {testResults.length > 0 && (
          <Alert title="测试日志" color="blue" style={{ maxHeight: 200, overflow: 'auto' }}>
            <Stack gap={2}>
              {testResults.slice(-10).map((result, index) => (
                <Text key={index} size="xs" style={{ fontFamily: 'monospace' }}>
                  {result}
                </Text>
              ))}
            </Stack>
          </Alert>
        )}

        <Text size="xs" c="dimmed">
          仅开发环境显示
        </Text>
      </Stack>
    </Paper>
  );
};

export default OnboardingTest;
