import React from 'react';
import { Button, Stack, Text, Paper, Group, Badge } from '@mantine/core';
import { useOnboarding } from './useOnboarding';
import { useLocation } from 'react-router-dom';

/**
 * 新手引导调试组件
 * 用于开发时测试和调试新手引导功能
 */
const OnboardingDebug: React.FC = () => {
  const location = useLocation();
  const {
    run,
    stepIndex,
    isCompleted,
    isSkipped,
    shouldShowTour,
    currentTourType,
    shouldStartFlag,
    startTour,
    stopTour,
    resetTour,
    skipTour,
    startSpecificTour,
    markShouldStartOnboarding,
  } = useOnboarding();

  return (
    <Paper p="md" withBorder style={{ position: 'fixed', top: 20, right: 20, zIndex: 10001, width: 300 }}>
      <Stack gap="xs">
        <Text size="sm" fw={600}>新手引导调试面板</Text>
        
        <Group gap="xs">
          <Text size="xs">当前页面:</Text>
          <Badge size="xs" variant="light">{location.pathname}</Badge>
        </Group>

        <Group gap="xs">
          <Text size="xs">引导类型:</Text>
          <Badge size="xs" color={currentTourType === 'main' ? 'blue' : currentTourType === 'chat' ? 'green' : 'orange'}>
            {currentTourType}
          </Badge>
        </Group>

        <Group gap="xs">
          <Text size="xs">运行状态:</Text>
          <Badge size="xs" color={run ? 'green' : 'gray'}>
            {run ? '运行中' : '已停止'}
          </Badge>
        </Group>

        <Group gap="xs">
          <Text size="xs">当前步骤:</Text>
          <Badge size="xs">{stepIndex}</Badge>
        </Group>

        <Group gap="xs">
          <Text size="xs">已完成:</Text>
          <Badge size="xs" color={isCompleted ? 'green' : 'gray'}>
            {isCompleted ? '是' : '否'}
          </Badge>
        </Group>

        <Group gap="xs">
          <Text size="xs">已跳过:</Text>
          <Badge size="xs" color={isSkipped ? 'orange' : 'gray'}>
            {isSkipped ? '是' : '否'}
          </Badge>
        </Group>

        <Group gap="xs">
          <Text size="xs">应该显示:</Text>
          <Badge size="xs" color={shouldShowTour ? 'blue' : 'gray'}>
            {shouldShowTour ? '是' : '否'}
          </Badge>
        </Group>

        <Group gap="xs">
          <Text size="xs">启动标记:</Text>
          <Badge size="xs" color={shouldStartFlag ? 'yellow' : 'gray'}>
            {shouldStartFlag ? '是' : '否'}
          </Badge>
        </Group>

        <Stack gap="xs">
          <Group gap="xs">
            <Button size="xs" onClick={startTour} disabled={run}>
              开始
            </Button>
            <Button size="xs" onClick={stopTour} disabled={!run}>
              停止
            </Button>
          </Group>
          
          <Button size="xs" onClick={resetTour} color="blue">
            重置引导
          </Button>
          
          <Button size="xs" onClick={skipTour} disabled={!run} color="orange">
            跳过
          </Button>
          
          <Group gap="xs">
            <Button size="xs" onClick={() => startSpecificTour('main')} variant="light">
              主引导
            </Button>
            <Button size="xs" onClick={() => startSpecificTour('chat')} variant="light">
              聊天引导
            </Button>
            <Button size="xs" onClick={() => startSpecificTour('agent')} variant="light">
              智能体引导
            </Button>
          </Group>

          <Group gap="xs">
            <Button
              size="xs"
              onClick={() => {
                localStorage.removeItem('aizy-onboarding-completed');
                localStorage.removeItem('aizy-onboarding-skipped');
                localStorage.setItem('aizy-onboarding-should-start', 'true');
                window.location.reload();
              }}
              color="red"
              variant="light"
            >
              清除状态并刷新
            </Button>

            <Button
              size="xs"
              onClick={markShouldStartOnboarding}
              color="yellow"
              variant="light"
            >
              模拟登录触发
            </Button>
          </Group>
        </Stack>

        <Text size="xs" c="dimmed">
          仅开发环境显示
        </Text>
      </Stack>
    </Paper>
  );
};

export default OnboardingDebug;
