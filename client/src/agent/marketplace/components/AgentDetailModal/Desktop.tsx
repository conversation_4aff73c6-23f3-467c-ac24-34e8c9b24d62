import React from 'react';
import { Modal, Image } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { getDetail, getBundleAgentsDetails } from '../../api';
import { AgentDetail, AgentType } from '../../schemas';
import { AgentStats, AgentConversationStarters, AgentFeatures, AgentRatings, RelatedAgents } from '../../components';
import { useAgentPayment } from '../../hooks';
import { FaAlipay, FaWeixin } from 'react-icons/fa';
import { PaymentPlanList } from '~/paymentPlan/components';
import './styles.css';
import { useChatContext } from '~/agent/chat/contexts';
import BundleAgentsList from '../BundleAgentsList';

interface DesktopAgentDetailModalProps {
  /**
   * 是否打开弹框
   */
  opened: boolean;
  /**
   * 关闭弹框的回调函数
   */
  onClose: () => void;
  /**
   * 智能体ID
   */
  agentId: string;
}

/**
 * 智能体详情弹框组件 - 桌面版
 */
const Desktop: React.FC<DesktopAgentDetailModalProps> = ({ opened, onClose, agentId }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';
  const { setAgent, setConversation, setMessages } = useChatContext();

  // 获取智能体详情
  const { data: agentDetailResponse, isLoading } = useQuery({
    queryKey: ['agentDetail', agentId],
    queryFn: () => getDetail(agentId),
    enabled: opened && !!agentId,
    refetchOnWindowFocus: false,
  });

  const agentDetail = agentDetailResponse?.data as AgentDetail;

  // 获取捆绑包智能体详情（仅当智能体是BUNDLE类型且已购买时）
  const { data: bundleAgents, isLoading: isBundleAgentsLoading } = useQuery({
    queryKey: ['bundleAgents', agentDetail?.bundle_agent_ids],
    queryFn: () => getBundleAgentsDetails(agentDetail?.bundle_agent_ids || []),
    enabled: !!(
      agentDetail?.has_purchased &&
      agentDetail?.mode === AgentType.BUNDLE &&
      agentDetail?.bundle_agent_ids &&
      agentDetail.bundle_agent_ids.length > 0
    ),
    refetchOnWindowFocus: false,
  });
  // 隐藏评级块和功能块
  const show = false;


  // 使用支付Hook
  const {
    paymentMethod,
    handleAgentPurchase,
    isPaymentLoading,
    selectedPlan,
    paymentPlans,
    selectedPlanId,
    handleSelectPlan,
    isPaymentPlansLoading,
  } = useAgentPayment(agentId);

  return (
    <Modal.Root
      opened={opened}
      onClose={onClose}
      centered
      size="xl"
      padding={0}
      styles={{
        root: {
          '--modal-radius': '16px',
        },
        content: {
          borderRadius: 'var(--modal-radius)',
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          maxHeight: '90vh',
        },
        body: {
          padding: 0,
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          flex: 1,
        },
        header: {
          display: 'none',
        },
      }}
    >
      <Modal.Overlay />
      <Modal.Content>
        <div className="relative flex flex-col h-full">
          {/* 顶部操作按钮 - 固定在顶部 */}
          <div
            className="sticky top-0 z-20 flex items-center justify-end bg-linear-to-b pb-2 px-4 pt-4"
            style={{
              backgroundColor: isDark ? '#1a202c' : 'white',
            }}
          >
            <button
              className="hover:bg-gray-100 dark:hover:bg-gray-800 flex h-8 w-8 items-center justify-center rounded-full"
              onClick={onClose}
              aria-label="关闭"
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M5.63603 5.63604C6.02656 5.24552 6.65972 5.24552 7.05025 5.63604L12 10.5858L16.9497 5.63604C17.3403 5.24552 17.9734 5.24552 18.364 5.63604C18.7545 6.02657 18.7545 6.65973 18.364 7.05025L13.4142 12L18.364 16.9497C18.7545 17.3403 18.7545 17.9734 18.364 18.364C17.9734 18.7545 17.3403 18.7545 16.9497 18.364L12 13.4142L7.05025 18.364C6.65972 18.7545 6.02656 18.7545 5.63603 18.364C5.24551 17.9734 5.24551 17.3403 5.63603 16.9497L10.5858 12L5.63603 7.05025C5.24551 6.65973 5.24551 6.02657 5.63603 5.63604Z"
                  fill="currentColor"
                />
              </svg>
            </button>
          </div>

          {/* 内容区域 - 可滚动，但留出底部按钮的空间 */}
          <div className="flex-1 overflow-y-auto px-8 py-6" style={{ paddingBottom: '80px', maxHeight: 'calc(90vh - 120px)' }}>
            {/* 智能体信息 */}
            <div className="flex h-full flex-col items-center justify-center h-fit">
              {/* 智能体图标 */}
              <div className="relative">
                <div className="mb-3 h-20 w-20">
                  <div className="gizmo-shadow-stroke overflow-hidden rounded-full">
                    <Image
                      className="flex-none"
                      radius={80}
                      w={80}
                      h={80}
                      src={agentDetail?.icon || 'https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1743067298-ai.jpg'}
                    />
                  </div>
                </div>
              </div>

              {/* 智能体名称和创建者 */}
              <div className="flex flex-col items-center gap-2">
                <div className="text-center text-2xl font-semibold">{isLoading ? '加载中...' : agentDetail?.name}</div>
                <div className="flex items-center gap-1 text-gray-500">
                  <div className="flex flex-row items-center space-x-1">
                    <div className="text-sm">创建者：{agentDetail?.created_by || 'AI助手'}</div>
                  </div>
                </div>
              </div>

              {/* 智能体描述 */}
              <div className="max-w-md text-center text-sm font-normal mt-2">
                {agentDetail?.description || '这是一个智能AI助手，可以帮助您完成各种任务。'}
              </div>
            </div>

            {/* 统计信息 */}
            {show && (<AgentStats agentDetail={agentDetail} isDark={isDark} isLoading={isLoading} />)}

            {/* 对话开场白 */}
            <AgentConversationStarters agentDetail={agentDetail} isDark={isDark} isLoading={isLoading} />

            {/* 功能列表/付费计划列表/捆绑包智能体列表 */}
            {agentDetail?.has_purchased ? (
              // 如果智能体已购买
              agentDetail?.mode === AgentType.BUNDLE ? (
                // BUNDLE类型，显示捆绑包智能体列表
                <div className="px-4">
                  <div className="mb-4">
                    <h3 className={`text-lg font-semibold ${isDark ? 'text-white' : 'text-gray-900'}`}>
                      捆绑包智能体
                    </h3>
                    <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                      此捆绑包包含以下智能体，您可以直接使用：
                    </p>
                  </div>
                  <BundleAgentsList
                    agents={bundleAgents || []}
                    isLoading={isBundleAgentsLoading}
                    isDark={isDark}
                  />
                </div>
              ) : (
                // 其他类型，显示功能列表（当前被禁用）
                show && <AgentFeatures agentDetail={agentDetail} isDark={isDark} isLoading={isLoading} />
              )
            ) : (
              // 否则显示付费计划列表
              <PaymentPlanList
                plans={paymentPlans}
                isLoading={isLoading || isPaymentPlansLoading}
                selectedPlanId={selectedPlanId}
                onSelectPlan={handleSelectPlan}
              />
            )}

            {/* 评级 */}
            {show && <AgentRatings agentDetail={agentDetail} isDark={isDark} isLoading={isLoading} />}

            {/* 由同一创建者创建的更多智能体 */}
            <RelatedAgents agentDetail={agentDetail} isDark={isDark} agentId={agentId} onClose={onClose} isLoading={isLoading} />
          </div>

          {/* 底部聊天按钮 - 固定在底部 */}
          <div
            className="sticky bottom-0 left-0 right-0 z-20 p-4 mt-auto"
            style={{
              backgroundColor: isDark ? '#1a202c' : 'white',
            }}
          >
            {agentDetail?.has_purchased ? (
              // 已购买，根据智能体类型显示不同按钮
              agentDetail?.mode === AgentType.BUNDLE ? (
                // BUNDLE类型，不显示按钮（智能体列表中已有开始聊天按钮）
                null
              ) : agentDetail?.mode === AgentType.WORKFLOW ? (
                // 工作流类型，显示运行按钮
                <Link
                  className={`flex items-center justify-center gap-2 w-full py-3 rounded-full font-medium ${
                    isDark ? 'bg-gray-800 hover:bg-gray-700' : 'bg-black hover:bg-gray-800'
                  } text-white`}
                  to={`/workflow/${agentId}`}
                >
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M12 4C7.58172 4 4 7.58172 4 12C4 14.1941 4.88193 16.1802 6.31295 17.6265C6.6343 17.9513 6.69466 18.4526 6.45959 18.8443L5.76619 20H12C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4ZM2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22H4C3.63973 22 3.30731 21.8062 3.1298 21.4927C2.95229 21.1792 2.95715 20.7944 3.14251 20.4855L4.36137 18.4541C2.88894 16.7129 2 14.4595 2 12Z"
                      fill="currentColor"
                    />
                  </svg>
                  运行
                </Link>
              ) : (
                // 其他类型，显示开始聊天按钮
                <Link
                  className={`flex items-center justify-center gap-2 w-full py-3 rounded-full font-medium ${
                    isDark ? 'bg-gray-800 hover:bg-gray-700' : 'bg-black hover:bg-gray-800'
                  } text-white`}
                  to={`/chat/${agentId}`}
                  onClick={() => {
                    setAgent(agentDetail);
                    setConversation(undefined);
                    setMessages([]);
                  }}
                  data-tour="agent-chat"
                >
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M12 4C7.58172 4 4 7.58172 4 12C4 14.1941 4.88193 16.1802 6.31295 17.6265C6.6343 17.9513 6.69466 18.4526 6.45959 18.8443L5.76619 20H12C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4ZM2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22H4C3.63973 22 3.30731 21.8062 3.1298 21.4927C2.95229 21.1792 2.95715 20.7944 3.14251 20.4855L4.36137 18.4541C2.88894 16.7129 2 14.4595 2 12Z"
                      fill="currentColor"
                    />
                  </svg>
                  开始聊天
                </Link>
              )
            ) : (
              // 未购买，显示支付按钮
              <div className="flex flex-col gap-3" data-tour="agent-purchase">
                <button
                  className={`flex items-center justify-center gap-2 w-full py-3 rounded-full font-medium ${
                    isDark ? 'bg-gray-800 hover:bg-gray-700' : 'bg-black hover:bg-gray-800'
                  } text-white`}
                  onClick={() => {
                    if (agentDetail) {
                      handleAgentPurchase(agentDetail, 'alipay');
                    }
                  }}
                  disabled={paymentMethod === 'alipay' && isPaymentLoading}
                >
                  {paymentMethod === 'alipay' && isPaymentLoading ? (
                    <span>处理中...</span>
                  ) : (
                    <>
                      <FaAlipay size={20} color="#1677FF" />
                      支付宝 ¥{selectedPlan?.price || 0}
                    </>
                  )}
                </button>
                <button
                  className={`flex items-center justify-center gap-2 w-full py-3 rounded-full font-medium ${
                    isDark ? 'bg-gray-800 hover:bg-gray-700' : 'bg-black hover:bg-gray-800'
                  } text-white`}
                  onClick={() => {
                    if (agentDetail) {
                      handleAgentPurchase(agentDetail, 'wechatpay');
                    }
                  }}
                  disabled={paymentMethod === 'wechatpay' && isPaymentLoading}
                >
                  {paymentMethod === 'wechatpay' && isPaymentLoading ? (
                    <span>处理中...</span>
                  ) : (
                    <>
                      <FaWeixin size={20} color="#09BB07" />
                      微信 ¥{selectedPlan?.price || 0}
                    </>
                  )}
                </button>
              </div>
            )}
          </div>
        </div>
      </Modal.Content>
    </Modal.Root>
  );
};

export default Desktop;
