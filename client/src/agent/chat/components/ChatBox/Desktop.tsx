import { Box, Group, Divider } from '@mantine/core';
import QuillTextArea from './components/QuillTextArea';
import MicAction from './components/MicAction';
import SendAction from './components/SendAction';
import StopAction from './components/StopAction';
import { useChatContext } from '../../contexts';
import CommandAction from './components/CommandAction';

const Desktop = () => {
  const { inputBackgroundColor, borderColor, isDragging, setIsDragging, isMessageSending } = useChatContext();

  // 处理拖拽事件
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isDragging) {
      setIsDragging(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };
  return (
    <Box
      pt={8}
      px={12}
      pb={12}
      bg={inputBackgroundColor}
      style={{
        border: isDragging ? `2px dashed #1677ff` : `1px solid ${borderColor}`,
        borderRadius: '20px',
        transition: 'all 0.2s ease',
        boxShadow: isDragging ? '0 0 10px rgba(22, 119, 255, 0.3)' : 'none',
      }}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      data-tour="chat-input"
    >
      <QuillTextArea /> {/* 使用QuillTextArea组件 */}
      <Group align="center" justify="space-between" mt="sm">
        <Group gap={8}>
          {/* <AttachmentAction />
          <DeepThinkAction />
          <WebSearchAction />
          <SkillsAction /> */}
          <CommandAction />
        </Group>
        <Group align="center" gap={0}>
          <MicAction />
          {/* <ImageAction /> */}
          <Divider
            orientation="vertical"
            ml={4}
            mr={12}
            style={{
              height: '24px',
              alignSelf: 'center',
              borderColor: borderColor,
            }}
          />
          <div data-tour="chat-send">
            {isMessageSending ? <StopAction /> : <SendAction aria-label="Send Message" />}
          </div>
        </Group>
      </Group>
    </Box>
  );
};

export default Desktop;
