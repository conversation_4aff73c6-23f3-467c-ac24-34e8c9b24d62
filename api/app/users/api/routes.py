import random
import string
from datetime import datetime

from billing import HkJingXiuBilling
from billing.activities import ActivityListResponse
from billing.keys import KeyListResponse
from fastapi import APIRouter, Body, Depends, HTTPException, Query
from sqlmodel import Session, or_, select


from app.db.models import Agent
from app.core import (
    ResponsePayloads,
    create_jwt_token,
    default_password_encoder,
    redis_client,
    sms_service,
    settings,
)
from app.core.exceptions import (
    UserNotFoundException,
    InvalidVerificationCodeException,
)
from app.db import User, get_session
from .schemas import (
    LoginRequest,
    LoginResult,
    RegisterRequest,
    ResetPasswordRequest,
    SmsLoginRequest,
    UserInfo,
)
from .utils import get_current_user

router = APIRouter(prefix="/users", tags=["用户"])


@router.post(
    "/login", summary="用户名密码登录", response_model=ResponsePayloads[LoginResult]
)
async def login(
    data: LoginRequest = Body(...), session: Session = Depends(get_session)
):
    """用户名密码登录"""
    # 查找用户
    statement = select(User).where(
        or_(User.username == data.username, User.phone == data.username)
    )
    user = session.exec(statement).first()

    # 用户不存在或密码错误
    if not user or not default_password_encoder.matches(data.password, user.password):
        raise HTTPException(status_code=401, detail="用户名或密码错误")

    # 生成token
    token = create_jwt_token({"sub": str(user.id)})

    return ResponsePayloads(
        data=LoginResult(
            token=token,
            user_info=UserInfo(
                id=user.id,
                username=user.phone if user.phone else user.username,
                nickname=user.nickname,
                phone=user.phone,
                can_create_agent=user.can_create_agent,
                is_allow_public_agent=user.is_allow_public_agent,
            ),
        )
    )


@router.get(
    "/me", summary="获取当前用户信息", response_model=ResponsePayloads[UserInfo]
)
async def get_me(current_user: User = Depends(get_current_user)):
    """获取当前用户信息"""
    return ResponsePayloads(
        data=UserInfo(
            id=current_user.id,
            username=(
                current_user.phone if current_user.phone else current_user.username
            ),
            nickname=current_user.nickname,
            phone=current_user.phone,
            can_create_agent=current_user.can_create_agent,
            is_allow_public_agent=current_user.is_allow_public_agent,
        )
    )


@router.post(
    "/login/sms", summary="短信验证码登录", response_model=ResponsePayloads[LoginResult]
)
async def login_with_sms(
    data: SmsLoginRequest = Body(...), session: Session = Depends(get_session)
):
    """短信验证码登录"""
    # 查找用户
    statement = select(User).where(User.phone == data.phone)
    user = session.exec(statement).first()

    # 从redis中获取验证码进行比对
    stored_code = redis_client.get(f"sms_code:{data.phone}")
    if not stored_code or stored_code != data.code:
        raise HTTPException(status_code=401, detail="验证码错误或已过期")

    # 如果用户不存在，自动创建用户
    if not user:
        # 生成随机用户名（使用手机号后4位加上随机字符）
        random_suffix = "".join(
            random.choices(string.ascii_lowercase + string.digits, k=4)
        )
        username = f"user_{data.phone[-4:]}_{random_suffix}"

        # 生成随机密码
        random_password = "".join(
            random.choices(string.ascii_letters + string.digits, k=12)
        )

        # 创建新用户
        user = User(
            username=username,
            password=default_password_encoder.encode(random_password),
            phone=data.phone,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        session.add(user)
        session.commit()
        session.refresh(user)

    # 生成token
    token = create_jwt_token({"sub": str(user.id)})

    return ResponsePayloads(
        data=LoginResult(
            token=token,
            user_info=UserInfo(
                id=user.id,
                username=user.phone if user.phone else user.username,
                nickname=user.nickname,
                phone=user.phone,
                can_create_agent=user.can_create_agent,
                is_allow_public_agent=user.is_allow_public_agent,
            ),
        )
    )


@router.get("/send_verification_code", summary="发送验证码")
async def send_verification_code(phone: str = Query(..., description="手机号")):
    """发送短信验证码"""
    # 生成6位随机数字验证码
    code = "".join(random.choices(string.digits, k=6))

    # 将验证码存储到redis，设置5分钟过期时间
    redis_client.set(f"sms_code:{phone}", code, ex=300)

    # 发送短信
    template_param = {"code": code}
    success = sms_service.send_sms(phone, template_param)

    if not success:
        raise HTTPException(status_code=500, detail="短信发送失败")

    return ResponsePayloads(data="success")


@router.post(
    "/register", summary="用户注册", response_model=ResponsePayloads[LoginResult]
)
async def register(
    data: RegisterRequest = Body(...), session: Session = Depends(get_session)
):
    """用户注册"""
    # 检查用户名是否已存在
    statement = select(User).where(User.username == data.username)
    existing_user = session.exec(statement).first()
    if existing_user:
        raise HTTPException(status_code=400, detail="用户名已存在")

    # 检查手机号是否已注册
    statement = select(User).where(User.phone == data.phone)
    existing_phone = session.exec(statement).first()
    if existing_phone:
        raise HTTPException(status_code=400, detail="手机号已注册")

    # 检查验证码
    stored_code = redis_client.get(f"sms_code:{data.phone}")
    if not stored_code or stored_code != data.code:
        raise HTTPException(status_code=400, detail="验证码错误或已过期")

    # 创建新用户
    new_user = User(
        username=data.username,
        password=default_password_encoder.encode(data.password),
        phone=data.phone,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )

    session.add(new_user)
    session.commit()
    session.refresh(new_user)

    # 生成登录token
    token = create_jwt_token({"sub": str(new_user.id)})

    return ResponsePayloads(
        data=LoginResult(
            token=token,
            user_info=UserInfo(
                id=new_user.id,
                username=data.phone,
                nickname=new_user.nickname,
                phone=new_user.phone,
                can_create_agent=new_user.can_create_agent,
            ),
        )
    )


@router.get(
    "/actives",
    summary="获取用户活动列表",
    response_model=ResponsePayloads[ActivityListResponse],
)
async def get_user_activities(
    key_id: str = Query(..., description="密钥ID"),
    page: int = Query(1, description="页码"),
    limit: int = Query(100, description="每页数量"),
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session),
):
    """获取用户活动列表"""

    # 创建billing客户端
    billing_client = HkJingXiuBilling(
        base_url=settings.hkjx_base_url, admin_key=settings.hkjx_appsecret
    )
    # 获取用户活动列表
    activities = await billing_client.activities.list_activities(
        key_id=key_id,
        page=page,
        limit=limit,
    )

    # 批量查询智能体名称
    # 1. 收集所有需要查询的智能体ID
    agent_ids = set()
    for activity in activities.activities:
        if activity.scope and len(activity.scope) > 0:
            agent_ids.update(activity.scope)

    # 2. 一次性查询所有智能体
    agent_map = {}
    if agent_ids:
        agents = session.exec(select(Agent).where(Agent.id.in_(list(agent_ids)))).all()
        agent_map = {agent.id: agent.name for agent in agents}

    # 3. 为每个活动添加scope_names
    for activity in activities.activities:
        if activity.scope:
            activity.scope_names = [agent_map.get(scope_id, scope_id) for scope_id in activity.scope]

    return ResponsePayloads(data=activities)


@router.get(
    "/keys",
    summary="获取用户密钥列表",
    response_model=ResponsePayloads[KeyListResponse],
)
async def get_user_keys(
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session),
):
    """获取用户密钥列表"""
    # 创建billing客户端
    billing_client = HkJingXiuBilling(
        base_url=settings.hkjx_base_url, admin_key=settings.hkjx_appsecret
    )

    # 获取用户所有密钥
    keys_response = await billing_client.keys.list_available_keys(
        user_id=str(current_user.id)
    )  # 为每个密钥获取对应的资源名称
    from app.users.utils import populate_key_resource_names

    populate_key_resource_names(keys_response.keys, session, current_user.id)

    return ResponsePayloads(data=keys_response)


@router.post("/reset-password", summary="重置密码")
async def reset_password(
    data: ResetPasswordRequest = Body(...), session: Session = Depends(get_session)
):
    """通过短信验证码重置密码"""
    # 查找用户
    statement = select(User).where(User.phone == data.phone)
    user = session.exec(statement).first()

    if not user:
        raise UserNotFoundException()

    # 验证短信验证码
    stored_code = redis_client.get(f"sms_code:{data.phone}")
    if not stored_code or stored_code != data.code:
        raise InvalidVerificationCodeException()

    # 更新密码
    user.password = default_password_encoder.encode(data.new_password)
    user.updated_at = datetime.now()
    session.add(user)
    session.commit()

    # 设置验证码过期
    redis_client.delete(f"sms_code:{data.phone}")

    return ResponsePayloads(data="success")
